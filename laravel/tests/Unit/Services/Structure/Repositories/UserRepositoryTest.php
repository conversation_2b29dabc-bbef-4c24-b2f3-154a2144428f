<?php

namespace Tests\Unit\Services\Structure\Repositories;

use App\Services\Structure\Repositories\UserRepository;
use App\Services\Structure\Repositories\UserRepositoryInterface;
use Tests\TestCase;

class UserRepositoryTest extends TestCase
{
    private UserRepository $repository;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = new UserRepository();
    }

    public function test_repository_implements_interface()
    {
        $this->assertInstanceOf(UserRepositoryInterface::class, $this->repository);
    }

    public function test_repository_has_required_methods()
    {
        $this->assertTrue(method_exists($this->repository, 'findDeepestDescendantUsers'));
        $this->assertTrue(method_exists($this->repository, 'getDeepestDescendantUsersAtLevel'));
        $this->assertTrue(method_exists($this->repository, 'findDeepestDescendantUsersCTE'));
        $this->assertTrue(method_exists($this->repository, 'findUsersAtAncestorLevelCTE'));
    }

    public function test_repository_class_exists()
    {
        $this->assertTrue(class_exists(UserRepository::class));
    }
}
