<?php

use App\Http\Controllers\AccountController;
use App\Http\Controllers\AppApiController;
use Illuminate\Support\Facades\Route;

Route::get('user', [AppApiController::class, 'userData'])->name('');
Route::get('user_data', [AppApiController::class, 'user_data'])->name('');
Route::get('master-data', [AppApiController::class, 'masterData'])->name('');
Route::get('master_data', [AppApiController::class, 'master_data'])->name('');
Route::get('accounts-doctors', [AppApiController::class, 'getAcountAndDoctors'])->name('');
Route::get('accounts_doctors', [AppApiController::class, 'get_account_and_doctors'])->name('');
Route::get('/plan-visits', [AppApiController::class, 'userPlans'])->name('');
Route::get('/pending-plan-visits', [AppApiController::class, 'getPendingPlans'])->name('');
Route::post('/save-approved-plans', [AppApiController::class, 'saveApprovedPlans'])->name('');
Route::get('/planned_visits', [AppApiController::class, 'user_plans'])->name('');
Route::get('/plan-ows', [AppApiController::class, 'planOw'])->name('');
Route::get('/planned_ows', [AppApiController::class, 'planOw'])->name('');
Route::post('/save-actuals', [AppApiController::class, 'saveActuals'])->name('');
Route::post('/save-plan', [AppApiController::class, 'savePlans'])->name('');
Route::post('/store_actual_visits', [AppApiController::class, 'store_actual_visits'])->name('');
Route::post('/save-ow', [AppApiController::class, 'saveAOw'])->name('');
Route::post('/store_office_works', [AppApiController::class, 'store_ow'])->name('');
Route::get('/app-presentations', [AppApiController::class, 'presentations'])->name('');
Route::get('/presentations_slides', [AppApiController::class, 'slides'])->name('');
Route::post('/store_location', [AppApiController::class, 'usageLogs'])->name('');

Route::prefix('v2')->group(function () {
    Route::post('/store_check_in_outs', [AppApiController::class, 'checkInOutV2'])->name('');
});

Route::post('/store_check_in_outs', [AppApiController::class, 'checkInOut'])->name('');
